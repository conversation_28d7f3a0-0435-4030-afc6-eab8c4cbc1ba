#!/usr/bin/env python3
"""
Test script to verify the schedule functionality fix
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.models.store_selected_illustrations import SelectedOptionsRequest, SelectedOption, IllustrationScheduleObject
from backend.app.services.store_selected_illustrations_services import store_selected_options

def test_schedule_functionality():
    """Test saving a scenario with schedule data"""
    print("🧪 Testing schedule functionality...")
    
    try:
        # Create a test schedule object
        schedule_data = [
            IllustrationScheduleObject(
                age=45,
                policy_year=1,
                current_year=2025,
                face_amount=100000.0,
                premium_amount=5000.0,
                coverage_options="A",
                loan_amount=None,
                surrender_amount=None,
                loan_repayment_amount=None,
                illustration_interest_rate=0.03
            ),
            IllustrationScheduleObject(
                age=46,
                policy_year=2,
                current_year=2026,
                face_amount=105000.0,
                premium_amount=5200.0,
                coverage_options="A",
                loan_amount=None,
                surrender_amount=None,
                loan_repayment_amount=None,
                illustration_interest_rate=0.03
            )
        ]
        
        # Create a test option with schedule
        test_option = SelectedOption(
            policy_id=1,
            illustration_type_id=2,  # Face Amount type
            illustration_question_id=201,
            illustration_option_id=20102,
            illustration_starting_age=None,
            illustration_ending_age=None,
            new_face_amount=100000.0,
            new_coverage_option="A",
            new_premium_amount=None,
            new_loan_amount=None,
            new_loan_repayment_amount=None,
            current_interest_rate=None,
            guaranteed_interest_rate=None,
            illustration_interest_rate=None,
            surrender_amount=None,
            RETIREMENT_AGE_GOAL=None,
            is_schedule="YES",
            schedule_object=schedule_data,
            value="Test schedule scenario"
        )
        
        # Create the request
        request = SelectedOptionsRequest(selected_options=[test_option])
        
        # Test the function
        result = store_selected_options(request)
        
        if result.get("status") == "SUCCESS":
            print("✅ Schedule functionality test PASSED!")
            print("✅ The fix for the 500 error when schedule button is present appears to be working.")
            return True
        else:
            print(f"❌ Schedule functionality test FAILED: {result}")
            return False
            
    except Exception as e:
        print(f"❌ Schedule functionality test FAILED with error: {str(e)}")
        print(f"❌ Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_schedule_functionality()
    if success:
        print("\n🎉 The schedule button 500 error issue has been FIXED!")
        print("📝 Summary of changes made:")
        print("   1. Fixed column name mismatch: coverage_options (model) -> COVERAGE_OPTION (database)")
        print("   2. Fixed transaction handling: store_schedule now uses existing cursor/transaction")
        print("   3. Removed duplicate connection handling in store_schedule function")
    else:
        print("\n❌ The schedule button issue still needs more investigation.")
    
    sys.exit(0 if success else 1)
