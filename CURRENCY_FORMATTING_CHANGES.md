# Currency Formatting Updates

## Overview
Updated the application to format currency values like Excel:
- **Dollar symbol ($) on the left**
- **Amount right-aligned**
- **Always show .00 for whole numbers** (e.g., 2,500.00 instead of 2,500)

## Files Created/Modified

### 1. New Currency Formatter Utility
**File:** `src/utils/currencyFormatter.ts`
- `formatCurrency()` - Main currency formatting function
- `formatCurrencyForTable()` - Excel-like table formatting with separate symbol and amount
- `formatPercentage()` - Percentage formatting (e.g., 0.03 → 3.00%)
- `formatLargeNumber()` - Large number formatting with K/M/B suffixes
- `parseCurrency()` - Parse currency strings back to numbers

### 2. Updated Components
**Files Updated:**
- `src/components/dashboard/SelectedScenarios.tsx`
- `src/components/dashboard/PolicySelection.tsx`
- `src/components/dashboard/FaceAmountPage.tsx`
- `src/components/dashboard/PremiumPage.tsx`
- `src/components/dashboard/LoanRepaymentPage.tsx`

**Changes Made:**
- Replaced all `toLocaleString()` calls with `formatCurrency()`
- Updated table cells to use Excel-like formatting
- Added proper CSS classes for alignment
- Separated dollar symbol from amount for better alignment

### 3. CSS Styling Updates
**File:** `src/index.css`
- Added `.currency-container` for proper flex layout
- Added `.currency-symbol` and `.currency-amount` classes
- Added `.table-cell-currency`, `.table-cell-number`, `.table-cell-text` utilities
- Added `.monospace-numbers` for consistent number alignment
- Used `font-variant-numeric: tabular-nums` for better number alignment

## Key Features

### ✅ Excel-like Currency Display
```
Before: $2500
After:  $2,500.00
```

### ✅ Table Alignment
```
Before:
$2500
$12345

After:
$  2,500.00
$ 12,345.00
```

### ✅ Consistent Decimal Places
- All currency values show exactly 2 decimal places
- Whole numbers display as .00 (e.g., 2500 → 2,500.00)
- Fractional numbers preserve decimals (e.g., 2500.5 → 2,500.50)

### ✅ Proper Percentage Formatting
```
Before: 0.03
After:  3.00%
```

### ✅ Error Handling
- Null/undefined values → $0.00
- Invalid strings → $0.00
- Empty strings → $0.00

## Usage Examples

### Basic Currency Formatting
```typescript
import { formatCurrency } from '../utils/currencyFormatter';

formatCurrency(2500)        // "$2,500.00"
formatCurrency(1234567.89) // "$1,234,567.89"
formatCurrency(0)          // "$0.00"
```

### Table Currency Formatting
```typescript
import { formatCurrencyForTable } from '../utils/currencyFormatter';

const result = formatCurrencyForTable(2500);
// result.symbol = "$"
// result.amount = "2,500.00"
// result.fullValue = "$2,500.00"
```

### Percentage Formatting
```typescript
import { formatPercentage } from '../utils/currencyFormatter';

formatPercentage(0.03)   // "3.00%"
formatPercentage(0.125)  // "12.50%"
```

## Testing
- ✅ Build completes successfully
- ✅ No TypeScript errors
- ✅ All currency values display with .00 format
- ✅ Table alignment works correctly
- ✅ Dollar symbols are left-aligned, amounts are right-aligned

## Browser Compatibility
- Uses `toLocaleString('en-US')` for number formatting
- Supports all modern browsers
- Fallback handling for edge cases
- Consistent display across different locales
