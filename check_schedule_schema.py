#!/usr/bin/env python3
"""
Check database schema for ILLUSTRATION_SCHEDULE_TABLE
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.db.connection import get_connection

def check_schedule_table_schema():
    """Check the schema of ILLUSTRATION_SCHEDULE_TABLE"""
    print("🔍 Checking ILLUSTRATION_SCHEDULE_TABLE schema...")
    
    conn = get_connection()
    cursor = conn.cursor()
    
    try:
        # Get table schema
        cursor.execute("DESCRIBE ILLUSTRATION_SCHEDULE_TABLE")
        columns = cursor.fetchall()
        
        print(f"📋 Table has {len(columns)} columns:")
        for column in columns:
            print(f"  - {column[0]} ({column[1]}) - Null: {column[2]}, Key: {column[3]}, Default: {column[4]}")
        
        # Check if there are any existing records
        cursor.execute("SELECT COUNT(*) FROM ILLUSTRATION_SCHEDULE_TABLE")
        count = cursor.fetchone()[0]
        print(f"📊 Table has {count} existing records")
        
        if count > 0:
            # Show sample records
            cursor.execute("SELECT * FROM ILLUSTRATION_SCHEDULE_TABLE LIMIT 3")
            records = cursor.fetchall()
            print("📋 Sample records:")
            for i, record in enumerate(records):
                print(f"  Record {i+1}: {record}")
        
    except Exception as e:
        print(f"❌ Error checking schema: {str(e)}")
    
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    check_schedule_table_schema()
